#include <QTRSensors.h>
#include <WiFi.h>

// Arduino-Pico-Master WiFi configuration for Raspberry Pi Pico W (earlephilhower/arduino-pico)

QTRSensors qtr;
const uint8_t SensorCount = 8;
uint16_t sensorValues[SensorCount];

// Constantes PID para reta e curva (ajustáveis)
float Kp = 0.06, Ki = 0.0, Kd = 0.45;      // Reta
float Kp_c = 0.35, Ki_c = 0.0, Kd_c = 0.35;  // Curva

// Variáveis PID
int P, D, I, previousError, PIDvalue, error;
int lsp, rsp;
int lfspeed = 100;    // Velocidade para reta (ajustável)
int lfspeed_c = 100;  // Velocidade para curva (ajustável)

// Pinos dos motores
const uint8_t EN_A = 15, IN1_A = 14;
const uint8_t IN1_B = 16, EN_B = 17;

// Sensores de estado
const uint8_t Sensor1 = 26, Sensor2 = 27;

// Estados do sistema
enum EstadoSistema { ESTADO_CALIBRACAO, ESTADO_INICIAL, ESTADO_RETA, ESTADO_CURVA, ESTADO_PARADO };
EstadoSistema estadoAtual = ESTADO_CALIBRACAO;

// Controle de transições
bool condicao1Anterior = false, condicao2Anterior = false;
bool condicao2SensorAnterior = false; // Para controle do Sensor2 (parada)
int contadorTransicoes = 0;
int contadorTransicoesParada = 0; // Contador para transições de parada
const int TRANSICOES_PARA_MUDANCA = 2;

// WiFi e servidor HTTP simples (apenas no estado de calibração)
const char* ssid = "Galaxy A72 3FB2";
const char* password = "bawpa0052";
WiFiServer server(80);
bool isCalibrating = true;
bool wifiAtivo = false;

// Variáveis de status para visualização no browser
String estadoAtualString = "CALIBRAÇÃO";
int posicaoSensor = 0;
int valorSensor1 = 0;
int valorSensor2 = 0;
unsigned long tempoInicio = 0;
unsigned long tempoAtual = 0;
String leiturasSensores = "";

// Performance metrics
unsigned long loopCount = 0;
float frequenciaLoop = 0;

void setup() {
  Serial.begin(115200);
  delay(2000); // Aguardar estabilização do Serial

  Serial.println("=================================");
  Serial.println("Robô Seguidor de Linha - Pico W");
  Serial.println("Board: earlephilhower/arduino-pico");
  Serial.println("=================================");

  // Setup dos sensores
  setupSensors();

  // Setup dos motores
  setupMotors();

  // Inicializar variáveis de status
  tempoInicio = millis();

  Serial.println("Sistema iniciado - Estado de Calibração");
  Serial.println("WiFi será ativado apenas durante calibração");
}

void loop() {
  unsigned long loopStartTime = millis();

  // Handle HTTP server apenas durante calibração
  if (wifiAtivo && estadoAtual == ESTADO_CALIBRACAO) {
    handleHTTPRequests();
  }

  // Atualizar variáveis de status
  atualizarVariaveisStatus();

  // State machine
  switch (estadoAtual) {
    case ESTADO_CALIBRACAO:
      handleCalibrationState();
      break;
    case ESTADO_INICIAL:
      handleInitialState();
      break;
    case ESTADO_RETA:
    case ESTADO_CURVA:
      handleRunningState();
      break;
    case ESTADO_PARADO:
      handleStoppedState();
      break;
  }

  // Calcular frequência do loop
  loopCount++;
  if (loopCount % 100 == 0) {
    unsigned long tempoDecorrido = millis() - loopStartTime;
    frequenciaLoop = 1000.0 / (tempoDecorrido + 1);
  }
}

// Setup functions
void setupSensors(){
  qtr.setTypeRC();
  qtr.setSensorPins((const uint8_t[]){1, 2, 3, 4, 5, 6, 7, 8}, SensorCount);
  qtr.setEmitterPin(0);
}

void setupMotors() {
  pinMode(IN1_A, OUTPUT);
  pinMode(IN1_B, OUTPUT);
  pinMode(EN_A, OUTPUT);
  pinMode(EN_B, OUTPUT);
  pinMode(LED_BUILTIN, OUTPUT);

  analogWrite(IN1_A, 0);
  analogWrite(IN1_B, 0);
}

void setupWiFi() {
  // Raspberry Pi Pico W Access Point setup (earlephilhower/arduino-pico)
  WiFi.mode(WIFI_AP);

  // Configure Access Point
  bool apResult = WiFi.softAP(ssid, password);
  if (!apResult) {
    Serial.println("Erro ao criar Access Point!");
    wifiAtivo = false;
    return;
  }

  IPAddress IP = WiFi.softAPIP();
  Serial.print("Pico W WiFi ativado - IP: ");
  Serial.println(IP);

  // Start HTTP server
  server.begin();
  wifiAtivo = true;

  Serial.println("Servidor HTTP iniciado - acesse via browser");
  Serial.println("Rede: " + String(ssid) + " | Senha: " + String(password));
  Serial.println("Conecte-se à rede e acesse: http://" + IP.toString());
}

void desativarWiFi() {
  if (wifiAtivo) {
    // Stop server first
    server.stop();

    // Disconnect AP (earlephilhower specific)
    WiFi.softAPdisconnect(true);

    // Set WiFi to off mode
    WiFi.mode(WIFI_OFF);

    wifiAtivo = false;
    Serial.println("Pico W WiFi desativado - modo offline para máxima performance");
  }
}

// Função para atualizar variáveis de status
void atualizarVariaveisStatus() {
  tempoAtual = millis() - tempoInicio;
  posicaoSensor = qtr.readLineWhite(sensorValues);
  valorSensor1 = analogRead(Sensor1);
  valorSensor2 = analogRead(Sensor2);

  // Atualizar string do estado
  switch (estadoAtual) {
    case ESTADO_CALIBRACAO: estadoAtualString = "CALIBRAÇÃO"; break;
    case ESTADO_INICIAL: estadoAtualString = "INICIAL"; break;
    case ESTADO_RETA: estadoAtualString = "RETA"; break;
    case ESTADO_CURVA: estadoAtualString = "CURVA"; break;
    case ESTADO_PARADO: estadoAtualString = "PARADO"; break;
  }

  // Atualizar leituras dos sensores QTR
  leiturasSensores = "";
  for (uint8_t i = 0; i < SensorCount; i++) {
    leiturasSensores += String(sensorValues[i]);
    if (i < SensorCount - 1) leiturasSensores += ",";
  }
}

// State handling functions
void handleCalibrationState() {
  if (isCalibrating) {
    // Ativar WiFi apenas durante calibração
    if (!wifiAtivo) {
      setupWiFi();
    }

    digitalWrite(LED_BUILTIN, HIGH);
    for (uint16_t i = 0; i < 400; i++) {
      qtr.calibrate();
      // Permitir que o servidor web responda durante calibração
      if (wifiAtivo) {
        handleHTTPRequests();
      }
      delay(1);
    }
    digitalWrite(LED_BUILTIN, LOW);
    isCalibrating = false;
    Serial.println("Calibração completa");
  }

  if (!isCalibrating) {
    // Desativar WiFi após calibração
    desativarWiFi();
    estadoAtual = ESTADO_INICIAL;
    Serial.println("Mudando para Estado Inicial - WiFi desativado");
  }
}

void handleInitialState() {
  uint16_t position = qtr.readLineWhite(sensorValues);
  int sensor1Value = analogRead(Sensor1);

  if (sensor1Value < 500) {
    estadoAtual = ESTADO_RETA;
    contadorTransicoes = 0;
    Serial.println("Mudando para Estado Reta");
  }
}

void handleRunningState() {
  uint16_t position = qtr.readLineWhite(sensorValues);
  error = 3500 - position;

  int Valor_Sensor1 = analogRead(Sensor1);
  int Valor_Sensor2 = analogRead(Sensor2);

  gerenciarEstados(Valor_Sensor1, Valor_Sensor2);
  executarControlePID();
}

void handleStoppedState() {
  // Stop motors
  stopMotors();
}

void gerenciarEstados(int sensor1, int sensor2) {
  bool condicao1 = (sensor1 < 500);
  bool condicao2 = (sensor1 > 500);
  bool condicao2Sensor = (sensor2 < 500); // Condição para parada usando Sensor2

  if (estadoAtual != ESTADO_INICIAL && estadoAtual != ESTADO_PARADO) {
    // Detecta transições para mudança de estado (reta/curva) usando Sensor1
    if (condicao1Anterior && !condicao1 && condicao2) {
      contadorTransicoes++;
      Serial.print("Transição reta/curva detectada: ");
      Serial.println(contadorTransicoes);
    }
    if (condicao2Anterior && !condicao2 && condicao1) {
      contadorTransicoes++;
      Serial.print("Transição reta/curva detectada: ");
      Serial.println(contadorTransicoes);
    }

    // Detecta transições para parada usando Sensor2
    if (!condicao2SensorAnterior && condicao2Sensor) {
      contadorTransicoesParada++;
      Serial.print("Transição parada detectada: ");
      Serial.println(contadorTransicoesParada);
    }
    if (condicao2SensorAnterior && !condicao2Sensor) {
      contadorTransicoesParada++;
      Serial.print("Transição parada detectada: ");
      Serial.println(contadorTransicoesParada);
    }

    // Verificar se deve parar (prioridade sobre mudança de estado)
    if (contadorTransicoesParada >= TRANSICOES_PARA_MUDANCA) {
      estadoAtual = ESTADO_PARADO;
      contadorTransicoesParada = 0;
      contadorTransicoes = 0;
      Serial.println("Mudando para Estado PARADO");
    }
    // Verificar se deve mudar estado (reta/curva)
    else if (contadorTransicoes >= TRANSICOES_PARA_MUDANCA) {
      mudarEstado();
      contadorTransicoes = 0;
    }
  }

  condicao1Anterior = condicao1;
  condicao2Anterior = condicao2;
  condicao2SensorAnterior = condicao2Sensor;
}

void mudarEstado() {
  if (estadoAtual == ESTADO_RETA) {
    estadoAtual = ESTADO_CURVA;
    Serial.println("Mudando para Estado Curva");
  } else {
    estadoAtual = ESTADO_RETA;
    Serial.println("Mudando para Estado Reta");
  }
}

void executarControlePID() {
  // Seleciona parâmetros PID baseado no estado atual
  float kp, ki, kd, speed;
  if (estadoAtual == ESTADO_RETA) {
    kp = Kp; ki = Ki; kd = Kd; speed = lfspeed;
  } else {
    kp = Kp_c; ki = Ki_c; kd = Kd_c; speed = lfspeed_c;
  }

  // Cálculo PID
  P = error;
  I += error;
  D = error - previousError;

  PIDvalue = (kp * P) + (ki * I) + (kd * D);
  previousError = error;

  // Cálculo das velocidades
  lsp = speed - PIDvalue;
  rsp = speed + PIDvalue;

  // Limitação de velocidade
  lsp = constrain(lsp, -250, 250);
  rsp = constrain(rsp, -250, 250);

  // Controle dos motores
  controlarMotor(EN_A, IN1_A, rsp);
  controlarMotor(EN_B, IN1_B, lsp);
}

void controlarMotor(uint8_t pinEN, uint8_t pinIN, int speed) {
  if (speed > 0) {
    analogWrite(pinEN, speed);
    digitalWrite(pinIN, LOW);
  } else {
    analogWrite(pinIN, -speed);
    digitalWrite(pinEN, LOW);
  }
}

void stopMotors() {
  analogWrite(EN_A, 0);
  analogWrite(EN_B, 0);
  digitalWrite(IN1_A, LOW);
  digitalWrite(IN1_B, LOW);
}

// Servidor HTTP simples - Raspberry Pi Pico W (earlephilhower/arduino-pico)
void handleHTTPRequests() {
  WiFiClient client = server.accept();
  if (client) {
    String request = "";
    unsigned long timeout = millis() + 1000; // 1 segundo timeout

    // Ler requisição HTTP com timeout
    while (client.connected() && millis() < timeout) {
      if (client.available()) {
        String line = client.readStringUntil('\r');
        if (line.length() == 1) break; // Fim dos headers
        if (request.length() == 0) request = line; // Primeira linha da requisição
      }
      delay(1); // Pequeno delay para estabilidade
    }

    // Processar requisição
    if (request.indexOf("GET / ") >= 0) {
      sendHTMLPage(client);
    }
    else if (request.indexOf("GET /status") >= 0) {
      sendStatusResponse(client);
    }
    else if (request.indexOf("GET /update") >= 0) {
      processParameterUpdate(request);
      sendSimpleResponse(client, "Parâmetros atualizados");
    }
    else if (request.indexOf("GET /calibrate") >= 0) {
      estadoAtual = ESTADO_CALIBRACAO;
      isCalibrating = true;
      contadorTransicoes = 0;
      contadorTransicoesParada = 0;
      sendSimpleResponse(client, "Calibração iniciada");
      Serial.println("Calibração solicitada via web");
    }
    else if (request.indexOf("GET /start") >= 0) {
      if (estadoAtual == ESTADO_PARADO || estadoAtual == ESTADO_CALIBRACAO) {
        estadoAtual = ESTADO_INICIAL;
        contadorTransicoes = 0;
        contadorTransicoesParada = 0;
        sendSimpleResponse(client, "Robô iniciado");
        Serial.println("Robô iniciado via web");
      } else {
        sendSimpleResponse(client, "Robô já em execução");
      }
    }
    else if (request.indexOf("GET /stop") >= 0) {
      estadoAtual = ESTADO_PARADO;
      stopMotors();
      sendSimpleResponse(client, "Robô parado");
      Serial.println("Robô parado via web");
    }
    else {
      send404Response(client);
    }

    client.stop();
  }
}

// Funções auxiliares do servidor HTTP
void sendHTMLPage(WiFiClient &client) {
  // Enviar headers HTTP
  client.println("HTTP/1.1 200 OK");
  client.println("Content-Type: text/html");
  client.println("Connection: close");
  client.println();

  // Enviar HTML
  client.println("<!DOCTYPE html><html><head>");
  client.println("<title>Robot Status - Calibração</title>");
  client.println("<meta name='viewport' content='width=device-width, initial-scale=1'>");
  client.println("<meta http-equiv='refresh' content='3'>");  // Auto-refresh a cada 3 segundos
  client.println("<style>");
  client.println("body{font-family:Arial;margin:20px;background:#f0f0f0;}");
  client.println(".container{max-width:800px;margin:0 auto;}");
  client.println(".card{background:white;padding:20px;margin:10px 0;border-radius:8px;box-shadow:0 2px 4px rgba(0,0,0,0.1);}");
  client.println(".status-item{background:#e8f4fd;padding:10px;margin:5px 0;border-radius:5px;border-left:4px solid #007acc;}");
  client.println(".warning{background:#fff3cd;padding:10px;border-radius:5px;margin:10px 0;border-left:4px solid #ffc107;}");
  client.println("</style>");
  client.println("</head><body>");
  client.println("<div class='container'>");
  client.println("<h1>🤖 Robô Seguidor de Linha - Raspberry Pi Pico W</h1>");

  // Aviso sobre modo online
  client.println("<div class='warning'>");
  client.println("⚠️ <strong>Pico W Online:</strong> WiFi ativo apenas durante calibração.<br>");
  client.println("📦 <strong>Board Package:</strong> earlephilhower/arduino-pico<br>");
  client.println("📚 <strong>Bibliotecas:</strong> QTRSensors.h + WiFi.h apenas");
  client.println("</div>");

  // Status atual
  client.println("<div class='card'>");
  client.println("<h2>📊 Status Atual</h2>");
  client.println("<div class='status-item'><strong>Estado:</strong> " + estadoAtualString + "</div>");
  client.println("<div class='status-item'><strong>Posição Sensor:</strong> " + String(posicaoSensor) + "</div>");
  client.println("<div class='status-item'><strong>Erro PID:</strong> " + String(error) + "</div>");
  client.println("<div class='status-item'><strong>Valor PID:</strong> " + String(PIDvalue) + "</div>");
  client.println("<div class='status-item'><strong>Velocidade Esquerda:</strong> " + String(lsp) + "</div>");
  client.println("<div class='status-item'><strong>Velocidade Direita:</strong> " + String(rsp) + "</div>");
  client.println("<div class='status-item'><strong>Sensor 1:</strong> " + String(valorSensor1) + "</div>");
  client.println("<div class='status-item'><strong>Sensor 2:</strong> " + String(valorSensor2) + " (Parada)</div>");
  client.println("<div class='status-item'><strong>Transições:</strong> " + String(contadorTransicoes) + "</div>");
  client.println("<div class='status-item'><strong>Transições Parada:</strong> " + String(contadorTransicoesParada) + "</div>");
  client.println("<div class='status-item'><strong>Tempo Ativo:</strong> " + String(tempoAtual/1000) + "s</div>");
  client.println("<div class='status-item'><strong>Frequência Loop:</strong> " + String(frequenciaLoop, 1) + "Hz</div>");
  client.println("</div>");

  // Sensores QTR
  client.println("<div class='card'>");
  client.println("<h2>👁️ Sensores QTR</h2>");
  for (int i = 0; i < 8; i++) {
    client.println("<div>Sensor " + String(i+1) + ": " + String(sensorValues[i]) + "</div>");
  }
  client.println("</div>");

  // Controles (apenas durante calibração)
  if (estadoAtual == ESTADO_CALIBRACAO) {
    client.println("<div class='card'>");
    client.println("<h2>🎮 Controles Simples</h2>");
    client.println("<p><a href='/calibrate' style='background:#28a745;color:white;padding:10px 20px;text-decoration:none;border-radius:5px;margin:5px;display:inline-block;'>🔧 Recalibrar</a></p>");
    client.println("<p><a href='/start' style='background:#007acc;color:white;padding:10px 20px;text-decoration:none;border-radius:5px;margin:5px;display:inline-block;'>▶️ Iniciar</a></p>");
    client.println("<p><a href='/stop' style='background:#dc3545;color:white;padding:10px 20px;text-decoration:none;border-radius:5px;margin:5px;display:inline-block;'>⏹️ Parar</a></p>");
    client.println("<p><a href='/status' style='background:#6c757d;color:white;padding:10px 20px;text-decoration:none;border-radius:5px;margin:5px;display:inline-block;'>📊 Status Texto</a></p>");
    client.println("</div>");

    // Parâmetros atuais e info do Pico W
    client.println("<div class='card'>");
    client.println("<h2>⚙️ Parâmetros Atuais</h2>");
    client.println("<p><strong>Reta:</strong> Kp=" + String(Kp) + " Ki=" + String(Ki) + " Kd=" + String(Kd) + " Vel=" + String(lfspeed) + "</p>");
    client.println("<p><strong>Curva:</strong> Kp=" + String(Kp_c) + " Ki=" + String(Ki_c) + " Kd=" + String(Kd_c) + " Vel=" + String(lfspeed_c) + "</p>");
    client.println("<p><em>Ajuste via Serial Monitor ou modifique o código</em></p>");
    client.println("<hr>");
    client.println("<p><strong>🔧 Plataforma:</strong> Raspberry Pi Pico W</p>");
    client.println("<p><strong>� Board Package:</strong> earlephilhower/arduino-pico</p>");
    client.println("<p><strong>�📚 Bibliotecas:</strong> QTRSensors.h + WiFi.h apenas</p>");
    client.println("<p><strong>💾 Memória:</strong> 264KB RAM, 2MB Flash</p>");
    client.println("<p><strong>⚡ CPU:</strong> 133MHz Dual Core RP2040</p>");
    client.println("</div>");
  }

  client.println("</div></body></html>");
}

void sendStatusResponse(WiFiClient &client) {
  client.println("HTTP/1.1 200 OK");
  client.println("Content-Type: text/plain");
  client.println("Connection: close");
  client.println();

  String response = "Estado:" + estadoAtualString;
  response += "|Posicao:" + String(posicaoSensor);
  response += "|Erro:" + String(error);
  response += "|PID:" + String(PIDvalue);
  response += "|VelE:" + String(lsp);
  response += "|VelD:" + String(rsp);
  response += "|S1:" + String(valorSensor1);
  response += "|S2:" + String(valorSensor2);
  response += "|Trans:" + String(contadorTransicoes);
  response += "|TransParada:" + String(contadorTransicoesParada);
  response += "|Tempo:" + String(tempoAtual/1000);

  client.println(response);
}

void sendSimpleResponse(WiFiClient &client, String message) {
  client.println("HTTP/1.1 200 OK");
  client.println("Content-Type: text/plain");
  client.println("Connection: close");
  client.println();
  client.println(message);
}

void send404Response(WiFiClient &client) {
  client.println("HTTP/1.1 404 Not Found");
  client.println("Content-Type: text/plain");
  client.println("Connection: close");
  client.println();
  client.println("404 - Página não encontrada");
}

void processParameterUpdate(String request) {
  // Parsing simples de parâmetros URL
  if (request.indexOf("kp=") >= 0) {
    int start = request.indexOf("kp=") + 3;
    int end = request.indexOf("&", start);
    if (end == -1) end = request.indexOf(" ", start);
    Kp = request.substring(start, end).toFloat();
  }

  if (request.indexOf("ki=") >= 0) {
    int start = request.indexOf("ki=") + 3;
    int end = request.indexOf("&", start);
    if (end == -1) end = request.indexOf(" ", start);
    Ki = request.substring(start, end).toFloat();
  }

  if (request.indexOf("kd=") >= 0) {
    int start = request.indexOf("kd=") + 3;
    int end = request.indexOf("&", start);
    if (end == -1) end = request.indexOf(" ", start);
    Kd = request.substring(start, end).toFloat();
  }

  if (request.indexOf("speed=") >= 0) {
    int start = request.indexOf("speed=") + 6;
    int end = request.indexOf("&", start);
    if (end == -1) end = request.indexOf(" ", start);
    lfspeed = request.substring(start, end).toInt();
  }

  Serial.println("Parâmetros atualizados via HTTP simples:");
  Serial.println("Reta: Kp=" + String(Kp) + " Ki=" + String(Ki) + " Kd=" + String(Kd) + " Vel=" + String(lfspeed));
}
